<div class="search-result">
    <div class="search-result-top">
        <div class="content-left">
            <template x-if="queryParams.query">
                <h4>
                    <?php echo e(trans('storefront::products.search_results_for')); ?>

                    
                    <span x-text="queryParams.query"></span>
                </h4>
            </template>

            <template x-if="!queryParams.query && queryParams.brand">
                <h4 x-text="initialBrandName"></h4>
            </template>

            <template x-if="!queryParams.query && !queryParams.brand && queryParams.category">
                <h4 x-text="categoryName"></h4>
            </template>
            
            <template x-if="!queryParams.query && !queryParams.brand && !queryParams.category && queryParams.tag">
                <h4 x-text="initialTagName"></h4>
            </template>
            
            <template x-if="!queryParams.query && !queryParams.brand && !queryParams.category && !queryParams.tag">
                <h4><?php echo e(trans('storefront::products.shop')); ?></h4>
            </template>
        </div>

        <div class="content-right">
            <div class="sorting-bar">
                <div class="mobile-view-filter" @click.stop="$store.layout.openSidebarFilter()">
                    <i class="las la-sliders-h"></i>
    
                    <?php echo e(trans('storefront::products.filters')); ?>

                </div>

                <div class="view-type">
                    <button
                        type="submit"
                        class="btn btn-grid-view"
                        :class="{ active: viewMode === 'grid' }"
                        title="<?php echo e(trans('storefront::products.grid_view')); ?>"
                        @click="viewMode = 'grid'"
                    >
                        <i class="las la-th-large"></i>
                    </button>

                    <button
                        type="submit"
                        class="btn btn-list-view"
                        :class="{ active: viewMode === 'list' }"
                        title="<?php echo e(trans('storefront::products.list_view')); ?>"
                        @click="viewMode = 'list'"
                    >
                        <i class="las la-list"></i>
                    </button>
                </div>

                <div class="mobile-view-filter-dropdown">
                    <div class="form-group">
                        <div
                            x-data="{
                                open: false,
                                selected: '<?php echo e(request('sort', 'latest')); ?>',
                                selectedText: '<?php echo e(ucfirst(request('sort', 'latest'))); ?>'
                            }"
                            class="dropdown custom-dropdown"
                            @click.away="open = false"
                        >
                            <div
                                class="btn btn-secondary dropdown-toggle"
                                :class="{ active: open }"
                                @click="open = !open"
                            >
                                <span x-text="selectedText"><?php echo e(ucfirst(request('sort', 'latest'))); ?></span>
    
                                <i class="las la-angle-down"></i>
                            </div>
    
                            <ul
                                x-cloak
                                x-show="open"
                                x-transition
                                class="dropdown-menu"
                                :class="{ active: open }"
                            >
                                <div class="dropdown-menu-scroll">
                                    <?php $__currentLoopData = trans('storefront::products.sort_options'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li
                                            class="dropdown-item"
                                            :class="{ active: selected === '<?php echo e($key); ?>' }"
                                            @click="
                                                open = false;
                                                
                                                if (selected !== '<?php echo e($key); ?>') {
                                                    changeSort('<?php echo e($key); ?>');
                                                }
        
                                                selected = '<?php echo e($key); ?>';
                                                selectedText = $el.innerText;
                                            "
                                        >
                                            <?php echo e($value); ?>

                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </ul>
                        </div>
                    </div>
    
                    <div
                        x-data="{
                            open: false,
                            selected: <?php echo e(request('perPage', 20)); ?>

                        }"
                        class="dropdown custom-dropdown"
                        @click.away="open = false"
                    >
                        <div
                            class="btn btn-secondary dropdown-toggle"
                            :class="{ active: open }"
                            @click="open = !open"
                        >
                            <span x-text="selected"><?php echo e(request('perPage', 20)); ?></span>
    
                            <i class="las la-angle-down"></i>
                        </div>
    
                        <ul
                            x-cloak
                            x-show="open"
                            x-transition
                            class="dropdown-menu"
                            :class="{ active: open }"
                        >
                            <?php $__currentLoopData = trans('storefront::products.per_page_options'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li
                                    class="dropdown-item"
                                    :class="{ active: selected === <?php echo e($value); ?> }"
                                    @click="
                                        open = false;
                                        
                                        if (selected !== <?php echo e($value); ?>) {
                                            changePerPage(<?php echo e($value); ?>);
                                        }
    
                                        selected = <?php echo e($value); ?>;
                                    "
                                >
                                    <?php echo e($value); ?>

                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div
        class="search-result-middle"
        :class="{
            empty: emptyProducts,
            loading: fetchingProducts 
        }"
    >  
        <template x-if="!emptyProducts && viewMode === 'grid'">
            <?php echo $__env->make('storefront::public.products.index.grid_view_products', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </template>

        <template x-if="!emptyProducts && viewMode === 'list'">
            <?php echo $__env->make('storefront::public.products.index.list_view_products', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </template>
        
        <template x-if="!fetchingProducts && emptyProducts">
            <div class="empty-message">
                <?php echo $__env->make('storefront::public.products.index.empty_results_logo', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                <h2><?php echo e(trans('storefront::products.no_product_found')); ?></h2>
            </div>
        </template>
    </div>

    <template x-if="!emptyProducts">
        <div class="search-result-bottom">
            <span class="showing-results" x-text="showingResults"></span>

            <template x-if="products.total > queryParams.perPage">
                <?php echo $__env->make('storefront::public.partials.pagination', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </template>
        </div>
    </template>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/products/index/search_result.blade.php ENDPATH**/ ?>
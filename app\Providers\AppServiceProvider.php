<?php

namespace FleetCart\Providers;

use Illuminate\Support\Facades\URL;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON>\DotenvEditor\DotenvEditorServiceProvider;
use Modules\Product\Entities\Product;
use FleetCart\Observers\ProductObserver;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(): void
    {
        Product::observe(ProductObserver::class);
        /*
        |--------------------------------------------------------------------------
        | Configure default String length of the generated migrations
        |--------------------------------------------------------------------------
        | <PERSON><PERSON> uses the utf8mb4 character set by default, which includes
        | support for storing "emojis" in the database. if you are running a
        | version of MySQL older than the 5.7.7 release or MariaDB older than
        | the 10.2.2 release, you need to configure the default string length
        | generated by migrations in order for MySQL to create indexes for
        | them.
        */
        Schema::defaultStringLength(191);

        Paginator::useBootstrap();

        if (Request::secure()) {
            URL::forceScheme('https');
        }
    }


    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register(): void
    {
        if (!config('app.installed')) {
            $this->app->register(DotenvEditorServiceProvider::class);
        }
    }
}

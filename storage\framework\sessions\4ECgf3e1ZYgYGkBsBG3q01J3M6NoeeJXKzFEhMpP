a:7:{s:6:"_token";s:40:"OQgmSNQn9zGrt6Th8rIKDXcTsRdBOM6M8uC4GEsl";s:6:"locale";s:5:"ar_PS";s:3:"url";a:0:{}s:6:"_flash";a:2:{s:3:"old";a:0:{}s:3:"new";a:0:{}}s:14:"fleetcart_auth";s:32:"Npgxk5mJXDJ1Vt7Vghv2nBncZfSw8wlc";s:9:"_previous";a:1:{s:3:"url";s:46:"http://cars.test/ar_PS/admin/products/249/edit";}s:67:"4ECgf3e1ZYgYGkBsBG3q01J3M6NoeeJXKzFEhMpP_recently_viewed_cart_items";O:32:"Darryldecode\Cart\CartCollection":2:{s:8:" * items";a:1:{i:46;O:32:"Darryldecode\Cart\ItemCollection":3:{s:8:" * items";a:6:{s:2:"id";i:46;s:4:"name";s:28:"غطاء بصمة تشغيل";s:5:"price";d:30;s:8:"quantity";i:1;s:10:"attributes";O:41:"Darryldecode\Cart\ItemAttributeCollection":2:{s:8:" * items";a:1:{s:7:"product";O:32:"Modules\Product\Entities\Product":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:21:{s:2:"id";i:46;s:12:"tax_class_id";N;s:4:"slug";s:18:"ghtaaa-bsm-tshghyl";s:5:"price";s:7:"30.0000";s:13:"special_price";N;s:18:"special_price_type";s:5:"fixed";s:19:"special_price_start";N;s:17:"special_price_end";N;s:13:"selling_price";s:7:"30.0000";s:3:"sku";N;s:12:"manage_stock";i:0;s:3:"qty";N;s:8:"in_stock";i:1;s:6:"viewed";i:3;s:9:"is_active";i:1;s:8:"new_from";N;s:6:"new_to";N;s:10:"deleted_at";N;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";s:10:"is_virtual";i:0;}s:11:" * original";a:21:{s:2:"id";i:46;s:12:"tax_class_id";N;s:4:"slug";s:18:"ghtaaa-bsm-tshghyl";s:5:"price";s:7:"30.0000";s:13:"special_price";N;s:18:"special_price_type";s:5:"fixed";s:19:"special_price_start";N;s:17:"special_price_end";N;s:13:"selling_price";s:7:"30.0000";s:3:"sku";N;s:12:"manage_stock";i:0;s:3:"qty";N;s:8:"in_stock";i:1;s:6:"viewed";i:3;s:9:"is_active";i:1;s:8:"new_from";N;s:6:"new_to";N;s:10:"deleted_at";N;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";s:10:"is_virtual";i:0;}s:10:" * changes";a:1:{s:6:"viewed";i:3;}s:8:" * casts";a:9:{s:10:"is_virtual";s:7:"boolean";s:9:"is_active";s:7:"boolean";s:19:"special_price_start";s:8:"datetime";s:17:"special_price_end";s:8:"datetime";s:8:"new_from";s:8:"datetime";s:6:"new_to";s:8:"datetime";s:10:"start_date";s:8:"datetime";s:8:"end_date";s:8:"datetime";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:15:{i:0;s:10:"base_image";i:1;s:17:"additional_images";i:2;s:5:"media";i:3;s:15:"formatted_price";i:4;s:21:"formatted_price_range";i:5;s:28:"has_percentage_special_price";i:6;s:21:"special_price_percent";i:7;s:14:"rating_percent";i:8;s:17:"does_manage_stock";i:9;s:11:"is_in_stock";i:10;s:15:"is_out_of_stock";i:11;s:6:"is_new";i:12;s:7:"variant";i:13;s:16:"is_in_flash_sale";i:14;s:19:"flash_sale_end_date";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:11:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:43:"Modules\Product\Entities\ProductTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:20:"product_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:46;s:10:"product_id";i:46;s:6:"locale";s:5:"ar_PS";s:4:"name";s:28:"غطاء بصمة تشغيل";s:11:"description";s:1043:"<p data-start="123" data-end="308">كبسة تشغيل داخلية بتصميم أنيق وجودة ممتازة، تضيف مظهرًا راقيًا داخل السيارة وشعورًا بالفخامة مع كل تشغيل.<br data-start="228" data-end="231" />متوفرة بشعارات مختارة لبعض أنواع السيارات، أو بشكل سادة بدون شعار حسب الرغبة.</p>
<ul data-start="310" data-end="501">
<li data-start="310" data-end="347">
<p data-start="312" data-end="347">تصميم أنيق يضيف لمسة فخامة داخلية</p>
</li>
<li data-start="348" data-end="399">
<p data-start="350" data-end="399">جودة عالية جدًا ولا تسبب أي إزعاج أثناء التشغيل</p>
</li>
<li data-start="400" data-end="423">
<p data-start="402" data-end="423">سهلة التركيب وثابتة</p>
</li>
<li data-start="424" data-end="469">
<p data-start="426" data-end="469">متوفرة بشعارات مختارة أو بدون شعار (سادة)</p>
</li>
</ul>";s:17:"short_description";N;}s:11:" * original";a:6:{s:2:"id";i:46;s:10:"product_id";i:46;s:6:"locale";s:5:"ar_PS";s:4:"name";s:28:"غطاء بصمة تشغيل";s:11:"description";s:1043:"<p data-start="123" data-end="308">كبسة تشغيل داخلية بتصميم أنيق وجودة ممتازة، تضيف مظهرًا راقيًا داخل السيارة وشعورًا بالفخامة مع كل تشغيل.<br data-start="228" data-end="231" />متوفرة بشعارات مختارة لبعض أنواع السيارات، أو بشكل سادة بدون شعار حسب الرغبة.</p>
<ul data-start="310" data-end="501">
<li data-start="310" data-end="347">
<p data-start="312" data-end="347">تصميم أنيق يضيف لمسة فخامة داخلية</p>
</li>
<li data-start="348" data-end="399">
<p data-start="350" data-end="399">جودة عالية جدًا ولا تسبب أي إزعاج أثناء التشغيل</p>
</li>
<li data-start="400" data-end="423">
<p data-start="402" data-end="423">سهلة التركيب وثابتة</p>
</li>
<li data-start="424" data-end="469">
<p data-start="426" data-end="469">متوفرة بشعارات مختارة أو بدون شعار (سادة)</p>
</li>
</ul>";s:17:"short_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:17:"short_description";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:10:"variations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:8:"variants";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:10:"categories";O:26:"TypiCMS\NestableCollection":8:{s:8:" * items";a:1:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:25;s:9:"parent_id";N;s:4:"slug";s:23:"akssoarat-dakhly-mtnoaa";s:8:"position";i:7;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 17:42:44";s:10:"updated_at";s:19:"2025-07-02 02:14:23";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:10:{s:2:"id";i:25;s:9:"parent_id";N;s:4:"slug";s:23:"akssoarat-dakhly-mtnoaa";s:8:"position";i:7;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 17:42:44";s:10:"updated_at";s:19:"2025-07-02 02:14:23";s:16:"pivot_product_id";i:46;s:17:"pivot_category_id";i:25;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:5:"pivot";O:44:"Illuminate\Database\Eloquent\Relations\Pivot":33:{s:13:" * connection";N;s:8:" * table";s:18:"product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:10:"product_id";i:46;s:11:"category_id";i:25;}s:11:" * original";a:2:{s:10:"product_id";i:46;s:11:"category_id";i:25;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";O:32:"Modules\Product\Entities\Product":35:{s:13:" * connection";N;s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:8:" * casts";a:9:{s:10:"is_virtual";s:7:"boolean";s:9:"is_active";s:7:"boolean";s:19:"special_price_start";s:8:"datetime";s:17:"special_price_end";s:8:"datetime";s:8:"new_from";s:8:"datetime";s:6:"new_to";s:8:"datetime";s:10:"start_date";s:8:"datetime";s:8:"end_date";s:8:"datetime";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:13:{i:0;s:10:"base_image";i:1;s:17:"additional_images";i:2;s:5:"media";i:3;s:15:"formatted_price";i:4;s:21:"formatted_price_range";i:5;s:28:"has_percentage_special_price";i:6;s:21:"special_price_percent";i:7;s:14:"rating_percent";i:8;s:17:"does_manage_stock";i:9;s:11:"is_in_stock";i:10;s:15:"is_out_of_stock";i:11;s:6:"is_new";i:12;s:7:"variant";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:16:{i:0;s:12:"tax_class_id";i:1;s:4:"slug";i:2;s:3:"sku";i:3;s:5:"price";i:4;s:13:"special_price";i:5;s:18:"special_price_type";i:6;s:19:"special_price_start";i:7;s:17:"special_price_end";i:8;s:13:"selling_price";i:9;s:12:"manage_stock";i:10;s:3:"qty";i:11;s:8:"in_stock";i:12;s:10:"is_virtual";i:13;s:9:"is_active";i:14;s:8:"new_from";i:15;s:6:"new_to";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:3:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:17:"short_description";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;s:16:" * scoutMetadata";a:0:{}s:16:" * forceDeleting";b:0;}s:13:" * foreignKey";s:10:"product_id";s:13:" * relatedKey";s:11:"category_id";}s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:25;s:11:"category_id";i:25;s:6:"locale";s:5:"ar_PS";s:4:"name";s:44:"اكسسوارات داخلية متنوعة";}s:11:" * original";a:4:{s:2:"id";i:25;s:11:"category_id";i:25;s:6:"locale";s:5:"ar_PS";s:4:"name";s:44:"اكسسوارات داخلية متنوعة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;s:8:" * total";i:1;s:15:" * parentColumn";s:9:"parent_id";s:33:" * removeItemsWithMissingAncestor";b:1;s:14:" * indentChars";s:8:"    ";s:15:" * childrenName";s:5:"items";s:17:" * parentRelation";s:6:"parent";}s:4:"tags";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:10:"attributes";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:7:"options";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:30:"Modules\Option\Entities\Option":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:2:{i:0;s:12:"translations";i:1;s:6:"values";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:13;s:4:"type";s:12:"radio_custom";s:11:"is_required";i:0;s:9:"is_global";i:0;s:8:"position";i:1;s:10:"deleted_at";N;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:10:{s:2:"id";i:13;s:4:"type";s:12:"radio_custom";s:11:"is_required";i:0;s:9:"is_global";i:0;s:8:"position";i:1;s:10:"deleted_at";N;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";s:16:"pivot_product_id";i:46;s:15:"pivot_option_id";i:13;}s:10:" * changes";a:0:{}s:8:" * casts";a:3:{s:11:"is_required";s:7:"boolean";s:9:"is_global";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:3:{s:5:"pivot";O:44:"Illuminate\Database\Eloquent\Relations\Pivot":33:{s:13:" * connection";N;s:8:" * table";s:15:"product_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:10:"product_id";i:46;s:9:"option_id";i:13;}s:11:" * original";a:2:{s:10:"product_id";i:46;s:9:"option_id";i:13;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";O:32:"Modules\Product\Entities\Product":35:{s:13:" * connection";N;s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:8:" * casts";a:9:{s:10:"is_virtual";s:7:"boolean";s:9:"is_active";s:7:"boolean";s:19:"special_price_start";s:8:"datetime";s:17:"special_price_end";s:8:"datetime";s:8:"new_from";s:8:"datetime";s:6:"new_to";s:8:"datetime";s:10:"start_date";s:8:"datetime";s:8:"end_date";s:8:"datetime";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:13:{i:0;s:10:"base_image";i:1;s:17:"additional_images";i:2;s:5:"media";i:3;s:15:"formatted_price";i:4;s:21:"formatted_price_range";i:5;s:28:"has_percentage_special_price";i:6;s:21:"special_price_percent";i:7;s:14:"rating_percent";i:8;s:17:"does_manage_stock";i:9;s:11:"is_in_stock";i:10;s:15:"is_out_of_stock";i:11;s:6:"is_new";i:12;s:7:"variant";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:16:{i:0;s:12:"tax_class_id";i:1;s:4:"slug";i:2;s:3:"sku";i:3;s:5:"price";i:4;s:13:"special_price";i:5;s:18:"special_price_type";i:6;s:19:"special_price_start";i:7;s:17:"special_price_end";i:8;s:13:"selling_price";i:9;s:12:"manage_stock";i:10;s:3:"qty";i:11;s:8:"in_stock";i:12;s:10:"is_virtual";i:13;s:9:"is_active";i:14;s:8:"new_from";i:15;s:6:"new_to";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:3:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:17:"short_description";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;s:16:" * scoutMetadata";a:0:{}s:16:" * forceDeleting";b:0;}s:13:" * foreignKey";s:10:"product_id";s:13:" * relatedKey";s:9:"option_id";}s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:41:"Modules\Option\Entities\OptionTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:19:"option_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:13;s:9:"option_id";i:13;s:6:"locale";s:5:"ar_PS";s:4:"name";s:32:"اختار نوع السيارة";}s:11:" * original";a:4:{s:2:"id";i:13;s:9:"option_id";i:13;s:6:"locale";s:5:"ar_PS";s:4:"name";s:32:"اختار نوع السيارة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:6:"values";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:13:{i:0;O:35:"Modules\Option\Entities\OptionValue":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"option_values";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:57;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:1;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:2:"id";i:57;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:1;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:46:"Modules\Option\Entities\OptionValueTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"option_value_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:50;s:15:"option_value_id";i:57;s:6:"locale";s:5:"ar_PS";s:5:"label";s:24:"سادة دون شعار";}s:11:" * original";a:4:{s:2:"id";i:50;s:15:"option_value_id";i:57;s:6:"locale";s:5:"ar_PS";s:5:"label";s:24:"سادة دون شعار";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:5:"label";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:5:"price";i:1;s:10:"price_type";i:2;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:5:"label";}s:16:" * defaultLocale";N;}i:1;O:35:"Modules\Option\Entities\OptionValue":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"option_values";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:58;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:2;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:2:"id";i:58;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:2;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:46:"Modules\Option\Entities\OptionValueTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"option_value_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:51;s:15:"option_value_id";i:58;s:6:"locale";s:5:"ar_PS";s:5:"label";s:10:"سكودا";}s:11:" * original";a:4:{s:2:"id";i:51;s:15:"option_value_id";i:58;s:6:"locale";s:5:"ar_PS";s:5:"label";s:10:"سكودا";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:5:"label";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:5:"price";i:1;s:10:"price_type";i:2;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:5:"label";}s:16:" * defaultLocale";N;}i:2;O:35:"Modules\Option\Entities\OptionValue":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"option_values";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:59;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:3;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:2:"id";i:59;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:3;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:46:"Modules\Option\Entities\OptionValueTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"option_value_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:52;s:15:"option_value_id";i:59;s:6:"locale";s:5:"ar_PS";s:5:"label";s:12:"تويوتا";}s:11:" * original";a:4:{s:2:"id";i:52;s:15:"option_value_id";i:59;s:6:"locale";s:5:"ar_PS";s:5:"label";s:12:"تويوتا";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:5:"label";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:5:"price";i:1;s:10:"price_type";i:2;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:5:"label";}s:16:" * defaultLocale";N;}i:3;O:35:"Modules\Option\Entities\OptionValue":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"option_values";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:60;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:4;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:2:"id";i:60;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:4;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:46:"Modules\Option\Entities\OptionValueTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"option_value_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:53;s:15:"option_value_id";i:60;s:6:"locale";s:5:"ar_PS";s:5:"label";s:10:"مازدا";}s:11:" * original";a:4:{s:2:"id";i:53;s:15:"option_value_id";i:60;s:6:"locale";s:5:"ar_PS";s:5:"label";s:10:"مازدا";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:5:"label";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:5:"price";i:1;s:10:"price_type";i:2;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:5:"label";}s:16:" * defaultLocale";N;}i:4;O:35:"Modules\Option\Entities\OptionValue":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"option_values";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:61;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:5;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:2:"id";i:61;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:5;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:46:"Modules\Option\Entities\OptionValueTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"option_value_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:54;s:15:"option_value_id";i:61;s:6:"locale";s:5:"ar_PS";s:5:"label";s:16:"متسوبيشي";}s:11:" * original";a:4:{s:2:"id";i:54;s:15:"option_value_id";i:61;s:6:"locale";s:5:"ar_PS";s:5:"label";s:16:"متسوبيشي";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:5:"label";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:5:"price";i:1;s:10:"price_type";i:2;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:5:"label";}s:16:" * defaultLocale";N;}i:5;O:35:"Modules\Option\Entities\OptionValue":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"option_values";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:62;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:6;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:2:"id";i:62;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:6;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:46:"Modules\Option\Entities\OptionValueTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"option_value_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:55;s:15:"option_value_id";i:62;s:6:"locale";s:5:"ar_PS";s:5:"label";s:10:"نيسان";}s:11:" * original";a:4:{s:2:"id";i:55;s:15:"option_value_id";i:62;s:6:"locale";s:5:"ar_PS";s:5:"label";s:10:"نيسان";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:5:"label";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:5:"price";i:1;s:10:"price_type";i:2;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:5:"label";}s:16:" * defaultLocale";N;}i:6;O:35:"Modules\Option\Entities\OptionValue":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"option_values";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:63;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:7;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:2:"id";i:63;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:7;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:46:"Modules\Option\Entities\OptionValueTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"option_value_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:56;s:15:"option_value_id";i:63;s:6:"locale";s:5:"ar_PS";s:5:"label";s:12:"مرسيدس";}s:11:" * original";a:4:{s:2:"id";i:56;s:15:"option_value_id";i:63;s:6:"locale";s:5:"ar_PS";s:5:"label";s:12:"مرسيدس";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:5:"label";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:5:"price";i:1;s:10:"price_type";i:2;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:5:"label";}s:16:" * defaultLocale";N;}i:7;O:35:"Modules\Option\Entities\OptionValue":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"option_values";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:64;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:8;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:2:"id";i:64;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:8;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:46:"Modules\Option\Entities\OptionValueTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"option_value_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:57;s:15:"option_value_id";i:64;s:6:"locale";s:5:"ar_PS";s:5:"label";s:9:"بي ام";}s:11:" * original";a:4:{s:2:"id";i:57;s:15:"option_value_id";i:64;s:6:"locale";s:5:"ar_PS";s:5:"label";s:9:"بي ام";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:5:"label";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:5:"price";i:1;s:10:"price_type";i:2;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:5:"label";}s:16:" * defaultLocale";N;}i:8;O:35:"Modules\Option\Entities\OptionValue":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"option_values";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:65;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:9;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:2:"id";i:65;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:9;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:46:"Modules\Option\Entities\OptionValueTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"option_value_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:58;s:15:"option_value_id";i:65;s:6:"locale";s:5:"ar_PS";s:5:"label";s:6:"كيا";}s:11:" * original";a:4:{s:2:"id";i:58;s:15:"option_value_id";i:65;s:6:"locale";s:5:"ar_PS";s:5:"label";s:6:"كيا";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:5:"label";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:5:"price";i:1;s:10:"price_type";i:2;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:5:"label";}s:16:" * defaultLocale";N;}i:9;O:35:"Modules\Option\Entities\OptionValue":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"option_values";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:66;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:10;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:2:"id";i:66;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:10;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:46:"Modules\Option\Entities\OptionValueTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"option_value_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:59;s:15:"option_value_id";i:66;s:6:"locale";s:5:"ar_PS";s:5:"label";s:12:"هونداي";}s:11:" * original";a:4:{s:2:"id";i:59;s:15:"option_value_id";i:66;s:6:"locale";s:5:"ar_PS";s:5:"label";s:12:"هونداي";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:5:"label";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:5:"price";i:1;s:10:"price_type";i:2;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:5:"label";}s:16:" * defaultLocale";N;}i:10;O:35:"Modules\Option\Entities\OptionValue":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"option_values";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:67;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:11;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:2:"id";i:67;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:11;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:46:"Modules\Option\Entities\OptionValueTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"option_value_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:60;s:15:"option_value_id";i:67;s:6:"locale";s:5:"ar_PS";s:5:"label";s:8:"سيات";}s:11:" * original";a:4:{s:2:"id";i:60;s:15:"option_value_id";i:67;s:6:"locale";s:5:"ar_PS";s:5:"label";s:8:"سيات";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:5:"label";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:5:"price";i:1;s:10:"price_type";i:2;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:5:"label";}s:16:" * defaultLocale";N;}i:11;O:35:"Modules\Option\Entities\OptionValue":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"option_values";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:68;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:12;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:2:"id";i:68;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:12;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:46:"Modules\Option\Entities\OptionValueTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"option_value_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:61;s:15:"option_value_id";i:68;s:6:"locale";s:5:"ar_PS";s:5:"label";s:19:"فولكس فاجن";}s:11:" * original";a:4:{s:2:"id";i:61;s:15:"option_value_id";i:68;s:6:"locale";s:5:"ar_PS";s:5:"label";s:19:"فولكس فاجن";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:5:"label";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:5:"price";i:1;s:10:"price_type";i:2;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:5:"label";}s:16:" * defaultLocale";N;}i:12;O:35:"Modules\Option\Entities\OptionValue":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"option_values";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:69;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:13;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:2:"id";i:69;s:9:"option_id";i:13;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:13;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:46:"Modules\Option\Entities\OptionValueTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"option_value_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:62;s:15:"option_value_id";i:69;s:6:"locale";s:5:"ar_PS";s:5:"label";s:8:"اودي";}s:11:" * original";a:4:{s:2:"id";i:62;s:15:"option_value_id";i:69;s:6:"locale";s:5:"ar_PS";s:5:"label";s:8:"اودي";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:5:"label";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:5:"price";i:1;s:10:"price_type";i:2;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:5:"label";}s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:6:"option";i:1;s:4:"type";i:2;s:11:"is_required";i:3;s:9:"is_global";i:4;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * defaultLocale";N;s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:5:{i:0;O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:238;s:7:"user_id";i:1;s:8:"filename";s:12:"IMG_0332.JPG";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/Tp2IFS8DAPXSq3agygRj7g3T0yoyCa97f0Hg4763.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:6:"131689";s:10:"created_at";s:19:"2025-06-10 00:58:31";s:10:"updated_at";s:19:"2025-06-10 00:58:31";}s:11:" * original";a:17:{s:2:"id";i:238;s:7:"user_id";i:1;s:8:"filename";s:12:"IMG_0332.JPG";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/Tp2IFS8DAPXSq3agygRj7g3T0yoyCa97f0Hg4763.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:6:"131689";s:10:"created_at";s:19:"2025-06-10 00:58:31";s:10:"updated_at";s:19:"2025-06-10 00:58:31";s:15:"pivot_entity_id";i:46;s:13:"pivot_file_id";i:238;s:17:"pivot_entity_type";s:32:"Modules\Product\Entities\Product";s:8:"pivot_id";i:918;s:10:"pivot_zone";s:10:"base_image";s:16:"pivot_created_at";s:19:"2025-06-10 01:01:38";s:16:"pivot_updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:46;s:7:"file_id";i:238;s:2:"id";i:918;s:4:"zone";s:10:"base_image";s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:46;s:7:"file_id";i:238;s:2:"id";i:918;s:4:"zone";s:10:"base_image";s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";O:32:"Modules\Product\Entities\Product":35:{s:13:" * connection";N;s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:8:" * casts";a:9:{s:10:"is_virtual";s:7:"boolean";s:9:"is_active";s:7:"boolean";s:19:"special_price_start";s:8:"datetime";s:17:"special_price_end";s:8:"datetime";s:8:"new_from";s:8:"datetime";s:6:"new_to";s:8:"datetime";s:10:"start_date";s:8:"datetime";s:8:"end_date";s:8:"datetime";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:13:{i:0;s:10:"base_image";i:1;s:17:"additional_images";i:2;s:5:"media";i:3;s:15:"formatted_price";i:4;s:21:"formatted_price_range";i:5;s:28:"has_percentage_special_price";i:6;s:21:"special_price_percent";i:7;s:14:"rating_percent";i:8;s:17:"does_manage_stock";i:9;s:11:"is_in_stock";i:10;s:15:"is_out_of_stock";i:11;s:6:"is_new";i:12;s:7:"variant";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:16:{i:0;s:12:"tax_class_id";i:1;s:4:"slug";i:2;s:3:"sku";i:3;s:5:"price";i:4;s:13:"special_price";i:5;s:18:"special_price_type";i:6;s:19:"special_price_start";i:7;s:17:"special_price_end";i:8;s:13:"selling_price";i:9;s:12:"manage_stock";i:10;s:3:"qty";i:11;s:8:"in_stock";i:12;s:10:"is_virtual";i:13;s:9:"is_active";i:14;s:8:"new_from";i:15;s:6:"new_to";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:3:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:17:"short_description";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;s:16:" * scoutMetadata";a:0:{}s:16:" * forceDeleting";b:0;}s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:32:"Modules\Product\Entities\Product";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:1;O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:236;s:7:"user_id";i:1;s:8:"filename";s:12:"IMG_0329.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/pkCmHEmHRE98W1Ruq0IStRHP10Od7H1Ch3q7Pd6h.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:6:"135960";s:10:"created_at";s:19:"2025-06-10 00:58:30";s:10:"updated_at";s:19:"2025-06-10 00:58:30";}s:11:" * original";a:17:{s:2:"id";i:236;s:7:"user_id";i:1;s:8:"filename";s:12:"IMG_0329.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/pkCmHEmHRE98W1Ruq0IStRHP10Od7H1Ch3q7Pd6h.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:6:"135960";s:10:"created_at";s:19:"2025-06-10 00:58:30";s:10:"updated_at";s:19:"2025-06-10 00:58:30";s:15:"pivot_entity_id";i:46;s:13:"pivot_file_id";i:236;s:17:"pivot_entity_type";s:32:"Modules\Product\Entities\Product";s:8:"pivot_id";i:919;s:10:"pivot_zone";s:17:"additional_images";s:16:"pivot_created_at";s:19:"2025-06-10 01:01:38";s:16:"pivot_updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:46;s:7:"file_id";i:236;s:2:"id";i:919;s:4:"zone";s:17:"additional_images";s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:46;s:7:"file_id";i:236;s:2:"id";i:919;s:4:"zone";s:17:"additional_images";s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:1999;s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:32:"Modules\Product\Entities\Product";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:2;O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:234;s:7:"user_id";i:1;s:8:"filename";s:21:"الملف_٠٠٠.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/2Hsa3mN9PbbrTgW60hNOnKH7unPrgxdoFh4gdiRS.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:6:"232187";s:10:"created_at";s:19:"2025-06-10 00:58:30";s:10:"updated_at";s:19:"2025-06-10 00:58:30";}s:11:" * original";a:17:{s:2:"id";i:234;s:7:"user_id";i:1;s:8:"filename";s:21:"الملف_٠٠٠.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/2Hsa3mN9PbbrTgW60hNOnKH7unPrgxdoFh4gdiRS.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:6:"232187";s:10:"created_at";s:19:"2025-06-10 00:58:30";s:10:"updated_at";s:19:"2025-06-10 00:58:30";s:15:"pivot_entity_id";i:46;s:13:"pivot_file_id";i:234;s:17:"pivot_entity_type";s:32:"Modules\Product\Entities\Product";s:8:"pivot_id";i:920;s:10:"pivot_zone";s:17:"additional_images";s:16:"pivot_created_at";s:19:"2025-06-10 01:01:38";s:16:"pivot_updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:46;s:7:"file_id";i:234;s:2:"id";i:920;s:4:"zone";s:17:"additional_images";s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:46;s:7:"file_id";i:234;s:2:"id";i:920;s:4:"zone";s:17:"additional_images";s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:1999;s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:32:"Modules\Product\Entities\Product";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:3;O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:235;s:7:"user_id";i:1;s:8:"filename";s:12:"IMG_0333.JPG";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/cytI1BfLCuGsVV643oNdFtk98cumGPCSYuqHWhxE.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:6:"182664";s:10:"created_at";s:19:"2025-06-10 00:58:30";s:10:"updated_at";s:19:"2025-06-10 00:58:30";}s:11:" * original";a:17:{s:2:"id";i:235;s:7:"user_id";i:1;s:8:"filename";s:12:"IMG_0333.JPG";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/cytI1BfLCuGsVV643oNdFtk98cumGPCSYuqHWhxE.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:6:"182664";s:10:"created_at";s:19:"2025-06-10 00:58:30";s:10:"updated_at";s:19:"2025-06-10 00:58:30";s:15:"pivot_entity_id";i:46;s:13:"pivot_file_id";i:235;s:17:"pivot_entity_type";s:32:"Modules\Product\Entities\Product";s:8:"pivot_id";i:921;s:10:"pivot_zone";s:17:"additional_images";s:16:"pivot_created_at";s:19:"2025-06-10 01:01:38";s:16:"pivot_updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:46;s:7:"file_id";i:235;s:2:"id";i:921;s:4:"zone";s:17:"additional_images";s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:46;s:7:"file_id";i:235;s:2:"id";i:921;s:4:"zone";s:17:"additional_images";s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:1999;s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:32:"Modules\Product\Entities\Product";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:4;O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:237;s:7:"user_id";i:1;s:8:"filename";s:21:"الملف_٠٠٢.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/tsbPqp5xxSBEK0SqQgm03SiFPwIB4hYkmWAusXt1.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:6:"261803";s:10:"created_at";s:19:"2025-06-10 00:58:30";s:10:"updated_at";s:19:"2025-06-10 00:58:30";}s:11:" * original";a:17:{s:2:"id";i:237;s:7:"user_id";i:1;s:8:"filename";s:21:"الملف_٠٠٢.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/tsbPqp5xxSBEK0SqQgm03SiFPwIB4hYkmWAusXt1.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:6:"261803";s:10:"created_at";s:19:"2025-06-10 00:58:30";s:10:"updated_at";s:19:"2025-06-10 00:58:30";s:15:"pivot_entity_id";i:46;s:13:"pivot_file_id";i:237;s:17:"pivot_entity_type";s:32:"Modules\Product\Entities\Product";s:8:"pivot_id";i:922;s:10:"pivot_zone";s:17:"additional_images";s:16:"pivot_created_at";s:19:"2025-06-10 01:01:38";s:16:"pivot_updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:46;s:7:"file_id";i:237;s:2:"id";i:922;s:4:"zone";s:17:"additional_images";s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:46;s:7:"file_id";i:237;s:2:"id";i:922;s:4:"zone";s:17:"additional_images";s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:1999;s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:32:"Modules\Product\Entities\Product";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}s:28:" * escapeWhenCastingToString";b:0;}s:7:"reviews";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:5:"brand";O:28:"Modules\Brand\Entities\Brand":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:6:"brands";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:2:{i:0;s:4:"slug";i:1;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:"translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}s:4:"meta";O:30:"Modules\Meta\Entities\MetaData":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"meta_data";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:54;s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:46;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:11:" * original";a:5:{s:2:"id";i:54;s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:46;s:10:"created_at";s:19:"2025-06-10 01:01:38";s:10:"updated_at";s:19:"2025-06-10 01:01:38";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:2:{i:0;s:9:"entity_id";i:1;s:11:"entity_type";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:2:{i:0;s:10:"meta_title";i:1;s:16:"meta_description";}s:16:" * defaultLocale";N;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:16:{i:0;s:12:"tax_class_id";i:1;s:4:"slug";i:2;s:3:"sku";i:3;s:5:"price";i:4;s:13:"special_price";i:5;s:18:"special_price_type";i:6;s:19:"special_price_start";i:7;s:17:"special_price_end";i:8;s:13:"selling_price";i:9;s:12:"manage_stock";i:10;s:3:"qty";i:11;s:8:"in_stock";i:12;s:10:"is_virtual";i:13;s:9:"is_active";i:14;s:8:"new_from";i:15;s:6:"new_to";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:3:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:17:"short_description";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;s:16:" * scoutMetadata";a:0:{}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}s:10:"conditions";a:0:{}}s:28:" * escapeWhenCastingToString";b:0;s:9:" * config";a:6:{s:14:"format_numbers";b:0;s:8:"decimals";i:0;s:9:"dec_point";s:1:".";s:13:"thousands_sep";s:1:",";s:7:"storage";N;s:6:"events";N;}}}s:28:" * escapeWhenCastingToString";b:0;}}
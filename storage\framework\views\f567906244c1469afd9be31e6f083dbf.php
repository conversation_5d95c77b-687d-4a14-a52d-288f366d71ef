<?php if($latestProducts->isNotEmpty()): ?>
    <div class="vertical-products">
        <div class="vertical-products-header">
            <h4 class="section-title"><?php echo e(trans('storefront::products.latest_products')); ?></h4>
        </div>

        <div class="vertical-products-slider swiper" x-ref="latestProducts">
            <div x-cloak class="swiper-wrapper">
                <?php $__currentLoopData = $latestProducts->chunk(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $latestProductChunks): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="swiper-slide">
                        <div class="vertical-products-slide">
                            <?php $__currentLoopData = $latestProductChunks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $latestProduct): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div x-data="ProductCard(<?php echo e(json_encode($latestProduct)); ?>)" class="vertical-product-card">
                                    <a :href="productUrl" class="product-image">
                                        <img
                                            :src="baseImage"
                                            :class="{ 'image-placeholder': !hasBaseImage }"
                                            :alt="productName"
                                            loading="lazy"
                                        />

                                        <div class="product-image-layer"></div>
                                    </a>

                                    <div class="product-info">
                                        <a :href="productUrl" class="product-name">
                                            <h6 x-text="productName"></h6>
                                        </a>

                                        <?php echo $__env->make('storefront::public.partials.product_rating', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                                        <div class="product-price" x-html="productPrice"></div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/products/index/latest_products.blade.php ENDPATH**/ ?>
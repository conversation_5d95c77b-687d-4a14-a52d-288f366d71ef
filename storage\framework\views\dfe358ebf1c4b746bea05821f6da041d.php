<section x-data="FeaturedCategories(<?php echo e($featuredCategories['categories']); ?>)" class="featured-categories-wrap">
    <div class="container">
        <div class="featured-categories-header">
            <div class="featured-categories-text">
                <h2 class="title"><?php echo e($featuredCategories['title']); ?></h2>
                
                <span class="excerpt"><?php echo e($featuredCategories['subtitle']); ?></span>
            </div>

            <ul class="tabs featured-categories-tabs">
                <?php $__currentLoopData = $featuredCategories['categories']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $tab): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li
                        class="tab-item"
                        :class="classes(<?php echo e($key); ?>)"
                        @click="changeTab(<?php echo e($key); ?>)"
                    >
                        <div class="featured-category-image">
                            <?php if($tab['logo']->path): ?>
                                <img
                                    src="<?php echo e($tab['logo']->path); ?>"
                                    alt="Category logo"
                                    loading="lazy"
                                />
                            <?php else: ?>
                                <img
                                    src="<?php echo e(asset('build/assets/image-placeholder.png')); ?>"
                                    class="image-placeholder"
                                    alt="Category logo"
                                    loading="lazy"
                                />
                            <?php endif; ?>
                        </div>
                        
                        <span class="featured-category-name">
                            <?php echo e($tab['name']); ?>

                        </span>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>

        <div class="tab-content">
            <div class="featured-category-products products-slider swiper"> 
                <div class="swiper-wrapper">
                    <?php $__currentLoopData = range(0, 7); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skeleton): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="swiper-slide swiper-slide-skeleton">
                            <?php echo $__env->make('storefront::public.partials.product_card_skeleton', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <template x-for="product in products" :key="product.id">
                        <div class="swiper-slide">
                            <?php echo $__env->make('storefront::public.partials.product_card', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </div>
                    </template>
                </div>

                <div class="swiper-pagination"></div>
            </div>
        </div>
    </div>
</section>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/home/<USER>/featured_categories.blade.php ENDPATH**/ ?>
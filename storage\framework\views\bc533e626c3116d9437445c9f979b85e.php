<nav x-data="PrimaryMenu" class="primary-menu position-relative navbar navbar-expand-sm swiper">
    <ul class="navbar-nav mega-menu horizontal-megamenu swiper-wrapper"> 
        <?php $__currentLoopData = $primaryMenu->menus(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php echo $__env->make('storefront::public.layouts.navigation.menu', ['type' => 'primary_menu'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>

    <div class="swiper-button-next"></div>
    <div class="swiper-button-prev"></div>
</nav>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/layouts/navigation/primary_menu.blade.php ENDPATH**/ ?>
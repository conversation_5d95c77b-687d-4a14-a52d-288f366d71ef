<div x-data="ProductRating(<?php echo e($data ?? 'product'); ?>)" class="product-rating">
    <div class="back-stars">
        <i class="las la-star"></i>
        <i class="las la-star"></i>
        <i class="las la-star"></i>
        <i class="las la-star"></i>
        <i class="las la-star"></i>

        <div x-cloak class="front-stars" :style="{ width: ratingPercent + '%' }">
            <i class="las la-star"></i>
            <i class="las la-star"></i>
            <i class="las la-star"></i>
            <i class="las la-star"></i>
            <i class="las la-star"></i>
        </div>
    </div>

    <template x-if="hasReviewCount">
        <span class="rating-count" x-text="`${reviewCount}`"></span>
    </template>

    <template x-if="hasReviewCount">
        <div
            class="reviews"
            x-text="
                reviewCount > 1 ?
                '<?php echo e(trans('storefront::product_card.reviews')); ?>' :
                '<?php echo e(trans('storefront::product_card.review')); ?>'
            "
        >
        </div>
    </template>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/partials/product_rating.blade.php ENDPATH**/ ?>
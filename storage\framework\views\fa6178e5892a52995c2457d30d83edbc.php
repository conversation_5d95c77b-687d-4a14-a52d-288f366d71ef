<?php if(setting('storefront_most_searched_keywords_enabled') && !empty($mostSearchedKeywords)): ?>
    <div class="searched-keywords">
        <label><?php echo e(trans("storefront::layouts.most_searched")); ?></label>

        <ul class="list-inline searched-keywords-list">
            <?php $__currentLoopData = $mostSearchedKeywords; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mostSearchedKeyword): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li>
                    <a href="<?php echo e(route('products.index', ['query' => $mostSearchedKeyword])); ?>">
                        <?php echo e($mostSearchedKeyword); ?><?php echo e(!$loop->last ? ',' : ''); ?>

                    </a>
                </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/layouts/header/search_suggestions.blade.php ENDPATH**/ ?>
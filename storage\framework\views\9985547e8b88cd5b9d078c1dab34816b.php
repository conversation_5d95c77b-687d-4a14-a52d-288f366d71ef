<section x-data="ProductTabsTwo(<?php echo e($productTabsTwo['tabs']); ?>)" class="landscape-tab-products-wrap">
    <div class="container">
        <div class="landscape-right-tab-products-inner">
            <div class="tab-products-header">
                <h5 class="section-title"><?php echo e($productTabsTwo['title']); ?></h5>
    
                <div class="tab-products-header-overflow">
                    <ul class="tabs">
                        <?php $__currentLoopData = $productTabsTwo['tabs']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $tab): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li
                                class="tab-item"
                                :class="classes(<?php echo e($key); ?>)"
                                @click="changeTab(<?php echo e($key); ?>)"
                            >
                                <?php echo e($tab); ?>

                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
    
                    <hr> 
                </div>
            </div>
    
            <div class="tab-content">
                <div class="landscape-right-tab-products products-slider swiper">
                    <div class="swiper-wrapper">
                        <?php $__currentLoopData = range(0, 7); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skeleton): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="swiper-slide swiper-slide-skeleton">
                                <?php echo $__env->make('storefront::public.partials.product_card_skeleton', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        
                        <template x-for="product in products" :key="product.id">
                            <div class="swiper-slide">
                                <?php echo $__env->make('storefront::public.partials.product_card', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                        </template>
                    </div>
    
                    <div class="swiper-pagination"></div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/home/<USER>/product_tabs_two.blade.php ENDPATH**/ ?>
<li class="<?php echo e(mega_menu_classes($menu, $type)); ?>">
    <a
        href="<?php echo e($menu->url()); ?>"
        class="nav-link menu-item"
        target="<?php echo e($menu->target()); ?>"
        title="<?php echo e($menu->name()); ?>"
    >
        <?php if($menu->hasIcon()): ?>
            <span class="menu-item-icon">
                <i class="<?php echo e($menu->icon()); ?>"></i>
            </span>
        <?php endif; ?>

        <?php echo e($menu->name()); ?>


        <?php if($menu->hasSubMenus()): ?>
            <i class="las la-angle-right"></i>
        <?php endif; ?>
    </a>

    <?php if($menu->isFluid()): ?>
        <?php echo $__env->make('storefront::public.layouts.navigation.fluid', ['subMenus' => $menu->subMenus()], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php else: ?>
        <?php echo $__env->make('storefront::public.layouts.navigation.dropdown', ['subMenus' => $menu->subMenus()], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
</li>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/layouts/navigation/menu.blade.php ENDPATH**/ ?>
<?php return array (
  'astrotomic/laravel-translatable' => 
  array (
    'providers' => 
    array (
      0 => 'Astrotomic\\Translatable\\TranslatableServiceProvider',
    ),
  ),
  'cartalyst/sentinel' => 
  array (
    'aliases' => 
    array (
      'Reminder' => 'Cartalyst\\Sentinel\\Laravel\\Facades\\Reminder',
      'Sentinel' => 'Cartalyst\\Sentinel\\Laravel\\Facades\\Sentinel',
      'Activation' => 'Cartalyst\\Sentinel\\Laravel\\Facades\\Activation',
    ),
    'providers' => 
    array (
      0 => 'Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider',
    ),
  ),
  'darryldecode/cart' => 
  array (
    'aliases' => 
    array (
      'Cart' => 'Darryldecode\\Cart\\Facades\\CartFacade',
    ),
    'providers' => 
    array (
      0 => 'Darryldecode\\Cart\\CartServiceProvider',
    ),
  ),
  'intervention/image' => 
  array (
    'aliases' => 
    array (
      'Image' => 'Intervention\\Image\\Facades\\Image',
    ),
    'providers' => 
    array (
      0 => 'Intervention\\Image\\ImageServiceProvider',
    ),
  ),
  'jackiedo/dotenv-editor' => 
  array (
    'aliases' => 
    array (
      'DotenvEditor' => 'Jackiedo\\DotenvEditor\\Facades\\DotenvEditor',
    ),
    'providers' => 
    array (
      0 => 'Jackiedo\\DotenvEditor\\DotenvEditorServiceProvider',
    ),
  ),
  'laravel/legacy-factories' => 
  array (
    'providers' => 
    array (
      0 => 'Illuminate\\Database\\Eloquent\\LegacyFactoryServiceProvider',
    ),
  ),
  'laravel/scout' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Scout\\ScoutServiceProvider',
    ),
  ),
  'laravel/socialite' => 
  array (
    'aliases' => 
    array (
      'Socialite' => 'Laravel\\Socialite\\Facades\\Socialite',
    ),
    'providers' => 
    array (
      0 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    ),
  ),
  'maatwebsite/laravel-sidebar' => 
  array (
    'providers' => 
    array (
      0 => 'Maatwebsite\\Sidebar\\SidebarServiceProvider',
    ),
  ),
  'mcamara/laravel-localization' => 
  array (
    'aliases' => 
    array (
      'LaravelLocalization' => 'Mcamara\\LaravelLocalization\\Facades\\LaravelLocalization',
    ),
    'providers' => 
    array (
      0 => 'Mcamara\\LaravelLocalization\\LaravelLocalizationServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'paytm/js-checkout' => 
  array (
    'aliases' => 
    array (
      'Pay' => 'Paytm\\JsCheckout\\Facades\\Paytm',
    ),
    'providers' => 
    array (
      0 => 'Paytm\\JsCheckout\\PaytmServiceProvider',
    ),
  ),
  'silviolleite/laravelpwa' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelPWA\\Providers\\LaravelPWAServiceProvider',
    ),
  ),
  'spatie/laravel-honeypot' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Honeypot\\HoneypotServiceProvider',
    ),
  ),
  'spatie/laravel-newsletter' => 
  array (
    'aliases' => 
    array (
      'Newsletter' => 'Spatie\\Newsletter\\Facades\\Newsletter',
    ),
    'providers' => 
    array (
      0 => 'Spatie\\Newsletter\\NewsletterServiceProvider',
    ),
  ),
  'spatie/laravel-sitemap' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Sitemap\\SitemapServiceProvider',
    ),
  ),
  'swayok/alternative-laravel-cache' => 
  array (
    'providers' => 
    array (
      0 => 'AlternativeLaravelCache\\Provider\\AlternativeCacheStoresServiceProvider',
    ),
  ),
  'tightenco/ziggy' => 
  array (
    'providers' => 
    array (
      0 => 'Tighten\\Ziggy\\ZiggyServiceProvider',
    ),
  ),
  'yajra/laravel-datatables-oracle' => 
  array (
    'aliases' => 
    array (
      'DataTables' => 'Yajra\\DataTables\\Facades\\DataTables',
    ),
    'providers' => 
    array (
      0 => 'Yajra\\DataTables\\DataTablesServiceProvider',
    ),
  ),
);
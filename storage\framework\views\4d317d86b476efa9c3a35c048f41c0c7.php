<?php $__env->startSection('title', setting('store_tagline')); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->renderUnless(is_null($slider), 'storefront::public.home.sections.hero', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path'])); ?>

    <?php if(setting('storefront_features_section_enabled')): ?>
        <?php echo $__env->make('storefront::public.home.sections.home_features', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
    
    <?php if(setting('storefront_featured_categories_section_enabled')): ?>
        <?php echo $__env->make('storefront::public.home.sections.featured_categories', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <?php if(setting('storefront_three_column_full_width_banners_enabled')): ?>
        <?php echo $__env->make('storefront::public.home.sections.three_column_full_width_banner', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
    
    <?php if(setting('storefront_product_tabs_1_section_enabled')): ?>
        <?php echo $__env->make('storefront::public.home.sections.product_tabs_one', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <?php if(setting('storefront_top_brands_section_enabled') && $topBrands->isNotEmpty()): ?>
        <?php echo $__env->make('storefront::public.home.sections.top_brands', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
    
    <?php if(setting('storefront_flash_sale_and_vertical_products_section_enabled')): ?>
        <?php echo $__env->make('storefront::public.home.sections.flash_sale', [
            'flashSaleEnabled' => setting('storefront_active_flash_sale_campaign')
        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <?php if(setting('storefront_two_column_banners_enabled')): ?>
        <?php echo $__env->make('storefront::public.home.sections.two_column_banner', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
    
    <?php if(setting('storefront_product_grid_section_enabled')): ?>
        <?php echo $__env->make('storefront::public.home.sections.grid_products', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <?php if(setting('storefront_three_column_banners_enabled')): ?>
        <?php echo $__env->make('storefront::public.home.sections.three_column_banner', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
    
    <?php if(setting('storefront_product_tabs_2_section_enabled')): ?>
        <?php echo $__env->make('storefront::public.home.sections.product_tabs_two', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
    
    <?php if(setting('storefront_one_column_banner_enabled')): ?>
        <?php echo $__env->make('storefront::public.home.sections.one_column_banner', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <?php if(setting('storefront_blogs_section_enabled')): ?>
        <?php echo $__env->make('storefront::public.home.sections.blog', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('globals'); ?>
    <?php echo app('Illuminate\Foundation\Vite')([
        'modules/Storefront/Resources/assets/public/sass/pages/home/<USER>',
        'modules/Storefront/Resources/assets/public/js/pages/home/<USER>',
    ]); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('storefront::public.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/home/<USER>/ ?>
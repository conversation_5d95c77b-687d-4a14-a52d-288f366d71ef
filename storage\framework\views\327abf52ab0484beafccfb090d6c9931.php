<div x-show="hasAnyProduct" class="col-xl-6 col-lg-18">
    <template x-if="hasAnyProduct">
        <div class="daily-deals-wrap">
            <div class="daily-deals-header clearfix">
                <h4 class="section-title">
                    <?php echo $flashSale['title']; ?>

                </h4>
            </div>

            <div class="daily-deals swiper">
                <div class="swiper-wrapper">
                    <template
                        x-for="product in products"
                        :key="product.id"
                    >
                        <div class="swiper-slide">
                            <div x-data="FlashSaleProductCard(product)" class="daily-deals-inner">
                                <div class="daily-deals-top">
                                    <a :href="productUrl" class="product-image">
                                        <img
                                            :src="baseImage"
                                            :class="{ 'image-placeholder': !hasBaseImage }"
                                            :alt="productName"
                                            loading="lazy"
                                        />
                                    </a>
                                </div>

                                <?php echo $__env->make('storefront::public.partials.product_rating', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                                <a :href="productUrl" class="product-name">
                                    <h6 x-text="productName"></h6>
                                </a>

                                <div class="product-info">
                                    <div class="product-price">
                                        <span class="special-price" x-text="formatCurrency(specialPrice)"></span>
                            
                                        <span class="previous-price" x-text="formatCurrency(regularPrice)"></span>
                                    </div>
                                </div>

                                <?php echo $__env->make('storefront::public.home.sections.flash_sale.product_countdown', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                                <div class="deal-progress">
                                    <div class="deal-stock">
                                        <div class="stock-available">
                                            <?php echo e(trans("storefront::product_card.available")); ?>


                                            <span x-text="product.pivot.qty"></span>
                                        </div>

                                        <div class="stock-sold">
                                            <?php echo e(trans("storefront::product_card.sold")); ?>


                                            <span x-text="product.pivot.sold"></span>
                                        </div>
                                    </div>

                                    <div class="progress">
                                        <div class="progress-bar" :style="{ width: progress }"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>

                <div class="swiper-button-next">
                    <?php echo e(trans("storefront::layouts.next")); ?>

                </div>
                
                <div class="swiper-button-prev">
                    <?php echo e(trans("storefront::layouts.prev")); ?>

                </div>
            </div>
        </div>
    </template>
</div><?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/home/<USER>/flash_sale/products.blade.php ENDPATH**/ ?>
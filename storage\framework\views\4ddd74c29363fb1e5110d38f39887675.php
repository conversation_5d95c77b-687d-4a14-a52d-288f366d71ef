<ul class="list-inline" @click.stop>
    <?php $__currentLoopData = $subMenus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subMenu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li
            class="<?php echo e($subMenu->hasItems() ? 'dropdown sub-menu' : ''); ?>"
            @click="
                $($el).children('ul.list-inline').slideToggle(200);
                $($el).toggleClass('active');
            "
        >
            <a href="<?php echo e($subMenu->url()); ?>" target="<?php echo e($subMenu->target()); ?>" @click.stop>
                <?php echo e($subMenu->name()); ?>

            </a>

            <?php if($subMenu->hasItems()): ?>
                <i class="las la-angle-right"></i>
            <?php endif; ?>

            <?php if($subMenu->hasItems()): ?>
                <?php echo $__env->make('storefront::public.layouts.sidebar_menu.dropdown', ['subMenus' => $subMenu->items()], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
        </li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/layouts/sidebar_menu/dropdown.blade.php ENDPATH**/ ?>
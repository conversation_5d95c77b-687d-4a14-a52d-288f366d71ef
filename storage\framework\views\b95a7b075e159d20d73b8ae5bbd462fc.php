<ul class="list-inline">
    <?php $__currentLoopData = $subCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li :class="{ active: queryParams.category === '<?php echo e($subCategory->slug); ?>' }">
            <?php if($subCategory->items->isNotEmpty()): ?>
                <i
                    class="las la-angle-right"
                    @click="
                        $($el).toggleClass('open');
                        $($el).siblings('ul').slideToggle(200);
                    "
                >
                </i>
            <?php endif; ?>
            
            <a
                href="<?php echo e(route('categories.products.index', ['category' => $subCategory->slug])); ?>"
                @click.prevent='
                    changeCategory({
                        name: "<?php echo e($subCategory->name); ?>",
                        banner: <?php echo e($subCategory->banner); ?>,
                        slug: "<?php echo e($subCategory->slug); ?>"
                    })
                '
            >
                <?php echo e($subCategory->name); ?>

            </a>

            <?php if($subCategory->items->isNotEmpty()): ?>
                <?php echo $__env->make('storefront::public.products.index.browse_sub_categories', ['subCategories' => $subCategory->items], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
        </li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/products/index/browse_sub_categories.blade.php ENDPATH**/ ?>
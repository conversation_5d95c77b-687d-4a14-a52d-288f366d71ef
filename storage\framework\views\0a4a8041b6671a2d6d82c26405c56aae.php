<div class="grid-view-products">
    <template
        x-for="product in products.data"
        :key="uid()"
    >
        <div class="grid-view-products-item">
            <?php echo $__env->make('storefront::public.partials.product_card', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </template>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/products/index/grid_view_products.blade.php ENDPATH**/ ?>
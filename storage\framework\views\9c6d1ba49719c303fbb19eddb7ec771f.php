<div class="form-group variant-custom-selection">
    <div class="row">
        <div class="col-lg-18">
            <label>
                <?php echo $option->name .
                    ($option->is_required ? '<span>*</span>' : ''); ?>

            </label>
        </div>

        <div class="col-lg-18">
            <ul class="list-inline form-custom-radio custom-selection">
                <?php $__currentLoopData = $option->values; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li
                        :class="{ active: customRadioTypeOptionValueIsActive(<?php echo e($option->id); ?>, <?php echo e($value->id); ?>) }"
                        @click="syncCustomRadioTypeOptionValue(<?php echo e($option->id); ?>, <?php echo e($value->id); ?>)"
                    >
                        <?php echo e($value->label); ?>

                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>

            <template x-if="errors.has('<?php echo e("options.{$option->id}"); ?>')">
                <span class="error-message" x-text="errors.get('<?php echo e("options.{$option->id}"); ?>')"></span>
            </template>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/products/show/custom_options/radio_custom.blade.php ENDPATH**/ ?>
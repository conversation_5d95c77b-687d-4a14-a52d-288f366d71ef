<ul class="list-inline browse-categories">
    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li :class="{ active: queryParams.category === '<?php echo e($category->slug); ?>' }">
            <?php if($category->items->isNotEmpty()): ?>
                <i
                    class="las la-angle-right"
                    @click="
                        $($el).toggleClass('open');
                        $($el).siblings('ul').slideToggle(200);
                    "
                >
                </i>
            <?php endif; ?>
            
            <a
                href="<?php echo e(route('categories.products.index', ['category' => $category->slug])); ?>"
                @click.prevent='
                    changeCategory({
                        name: "<?php echo e(addslashes($category->name)); ?>",
                        banner: <?php echo e($category->banner); ?>,
                        slug: "<?php echo e($category->slug); ?>"
                    })
                '
            >
                <?php echo e($category->name); ?>

            </a>

            <?php if($category->items->isNotEmpty()): ?>
                <?php echo $__env->make('storefront::public.products.index.browse_sub_categories', ['subCategories' => $category->items], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
        </li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/products/index/browse_categories.blade.php ENDPATH**/ ?>
a:3:{i:0;O:30:"Modules\Slider\Entities\Slider":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"sliders";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:2:{i:0;s:12:"translations";i:1;s:6:"slides";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:4;s:5:"speed";i:1000;s:8:"autoplay";i:1;s:14:"autoplay_speed";i:3000;s:4:"fade";i:0;s:4:"dots";i:1;s:6:"arrows";i:1;s:10:"created_at";s:19:"2025-07-02 16:43:05";s:10:"updated_at";s:19:"2025-07-04 21:35:06";}s:11:" * original";a:9:{s:2:"id";i:4;s:5:"speed";i:1000;s:8:"autoplay";i:1;s:14:"autoplay_speed";i:3000;s:4:"fade";i:0;s:4:"dots";i:1;s:6:"arrows";i:1;s:10:"created_at";s:19:"2025-07-02 16:43:05";s:10:"updated_at";s:19:"2025-07-04 21:35:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:41:"Modules\Slider\Entities\SliderTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:19:"slider_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:4;s:9:"slider_id";i:4;s:6:"locale";s:5:"ar_PS";s:4:"name";s:10:"العرض";}s:11:" * original";a:4:{s:2:"id";i:4;s:9:"slider_id";i:4;s:6:"locale";s:5:"ar_PS";s:4:"name";s:10:"العرض";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:6:"slides";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:3:{i:0;O:35:"Modules\Slider\Entities\SliderSlide":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"slider_slides";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:2:{i:0;s:12:"translations";i:1;s:4:"file";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:9;s:9:"slider_id";i:4;s:7:"options";N;s:18:"call_to_action_url";N;s:18:"open_in_new_window";i:1;s:8:"position";i:0;s:10:"created_at";s:19:"2025-07-02 16:43:05";s:10:"updated_at";s:19:"2025-07-02 16:48:21";}s:11:" * original";a:8:{s:2:"id";i:9;s:9:"slider_id";i:4;s:7:"options";N;s:18:"call_to_action_url";N;s:18:"open_in_new_window";i:1;s:8:"position";i:0;s:10:"created_at";s:19:"2025-07-02 16:43:05";s:10:"updated_at";s:19:"2025-07-02 16:48:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:7:"options";s:5:"array";s:18:"open_in_new_window";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:46:"Modules\Slider\Entities\SliderSlideTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"slider_slide_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:9;s:15:"slider_slide_id";i:9;s:6:"locale";s:5:"ar_PS";s:7:"file_id";i:944;s:9:"caption_1";s:1:"3";s:9:"caption_2";N;s:19:"call_to_action_text";N;s:9:"direction";s:4:"left";}s:11:" * original";a:8:{s:2:"id";i:9;s:15:"slider_slide_id";i:9;s:6:"locale";s:5:"ar_PS";s:7:"file_id";i:944;s:9:"caption_1";s:1:"3";s:9:"caption_2";N;s:19:"call_to_action_text";N;s:9:"direction";s:4:"left";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:7:"file_id";i:1;s:9:"caption_1";i:2;s:9:"caption_2";i:3;s:9:"direction";i:4;s:19:"call_to_action_text";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:4:"file";O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:944;s:7:"user_id";i:1;s:8:"filename";s:44:"RNOVhCTE3NbPi2MdG14upDGc4ZnoV3o6fzT0m7Ye.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/UrG0IDtNtshLG8sy4mmZuvqHwZcexd1vCuRLHrhX.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:6:"196877";s:10:"created_at";s:19:"2025-07-02 16:42:13";s:10:"updated_at";s:19:"2025-07-02 16:42:13";}s:11:" * original";a:10:{s:2:"id";i:944;s:7:"user_id";i:1;s:8:"filename";s:44:"RNOVhCTE3NbPi2MdG14upDGc4ZnoV3o6fzT0m7Ye.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/UrG0IDtNtshLG8sy4mmZuvqHwZcexd1vCuRLHrhX.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:6:"196877";s:10:"created_at";s:19:"2025-07-02 16:42:13";s:10:"updated_at";s:19:"2025-07-02 16:42:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:7:"options";i:1;s:18:"call_to_action_url";i:2;s:18:"open_in_new_window";i:3;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:"translatedAttributes";a:5:{i:0;s:7:"file_id";i:1;s:9:"caption_1";i:2;s:9:"caption_2";i:3;s:9:"direction";i:4;s:19:"call_to_action_text";}s:16:" * defaultLocale";N;}i:1;O:35:"Modules\Slider\Entities\SliderSlide":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"slider_slides";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:2:{i:0;s:12:"translations";i:1;s:4:"file";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:10;s:9:"slider_id";i:4;s:7:"options";N;s:18:"call_to_action_url";N;s:18:"open_in_new_window";i:1;s:8:"position";i:1;s:10:"created_at";s:19:"2025-07-02 16:43:05";s:10:"updated_at";s:19:"2025-07-02 16:48:21";}s:11:" * original";a:8:{s:2:"id";i:10;s:9:"slider_id";i:4;s:7:"options";N;s:18:"call_to_action_url";N;s:18:"open_in_new_window";i:1;s:8:"position";i:1;s:10:"created_at";s:19:"2025-07-02 16:43:05";s:10:"updated_at";s:19:"2025-07-02 16:48:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:7:"options";s:5:"array";s:18:"open_in_new_window";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:46:"Modules\Slider\Entities\SliderSlideTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"slider_slide_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:10;s:15:"slider_slide_id";i:10;s:6:"locale";s:5:"ar_PS";s:7:"file_id";i:943;s:9:"caption_1";s:1:"2";s:9:"caption_2";N;s:19:"call_to_action_text";N;s:9:"direction";s:4:"left";}s:11:" * original";a:8:{s:2:"id";i:10;s:15:"slider_slide_id";i:10;s:6:"locale";s:5:"ar_PS";s:7:"file_id";i:943;s:9:"caption_1";s:1:"2";s:9:"caption_2";N;s:19:"call_to_action_text";N;s:9:"direction";s:4:"left";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:7:"file_id";i:1;s:9:"caption_1";i:2;s:9:"caption_2";i:3;s:9:"direction";i:4;s:19:"call_to_action_text";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:4:"file";O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:943;s:7:"user_id";i:1;s:8:"filename";s:44:"EXi9hUKkAQzRce66GRplkHuzU0g1zshQbgJ9w3EJ.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/xLuIaZCMAW55pVzVdMs8Esz6xdJAreOamywe2YIG.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:6:"204683";s:10:"created_at";s:19:"2025-07-02 16:42:12";s:10:"updated_at";s:19:"2025-07-02 16:42:12";}s:11:" * original";a:10:{s:2:"id";i:943;s:7:"user_id";i:1;s:8:"filename";s:44:"EXi9hUKkAQzRce66GRplkHuzU0g1zshQbgJ9w3EJ.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/xLuIaZCMAW55pVzVdMs8Esz6xdJAreOamywe2YIG.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:6:"204683";s:10:"created_at";s:19:"2025-07-02 16:42:12";s:10:"updated_at";s:19:"2025-07-02 16:42:12";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:7:"options";i:1;s:18:"call_to_action_url";i:2;s:18:"open_in_new_window";i:3;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:"translatedAttributes";a:5:{i:0;s:7:"file_id";i:1;s:9:"caption_1";i:2;s:9:"caption_2";i:3;s:9:"direction";i:4;s:19:"call_to_action_text";}s:16:" * defaultLocale";N;}i:2;O:35:"Modules\Slider\Entities\SliderSlide":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"slider_slides";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:2:{i:0;s:12:"translations";i:1;s:4:"file";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:11;s:9:"slider_id";i:4;s:7:"options";N;s:18:"call_to_action_url";N;s:18:"open_in_new_window";i:1;s:8:"position";i:2;s:10:"created_at";s:19:"2025-07-02 16:43:05";s:10:"updated_at";s:19:"2025-07-02 16:48:21";}s:11:" * original";a:8:{s:2:"id";i:11;s:9:"slider_id";i:4;s:7:"options";N;s:18:"call_to_action_url";N;s:18:"open_in_new_window";i:1;s:8:"position";i:2;s:10:"created_at";s:19:"2025-07-02 16:43:05";s:10:"updated_at";s:19:"2025-07-02 16:48:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:7:"options";s:5:"array";s:18:"open_in_new_window";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:46:"Modules\Slider\Entities\SliderSlideTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"slider_slide_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:11;s:15:"slider_slide_id";i:11;s:6:"locale";s:5:"ar_PS";s:7:"file_id";i:942;s:9:"caption_1";s:1:"1";s:9:"caption_2";N;s:19:"call_to_action_text";N;s:9:"direction";s:4:"left";}s:11:" * original";a:8:{s:2:"id";i:11;s:15:"slider_slide_id";i:11;s:6:"locale";s:5:"ar_PS";s:7:"file_id";i:942;s:9:"caption_1";s:1:"1";s:9:"caption_2";N;s:19:"call_to_action_text";N;s:9:"direction";s:4:"left";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:7:"file_id";i:1;s:9:"caption_1";i:2;s:9:"caption_2";i:3;s:9:"direction";i:4;s:19:"call_to_action_text";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:4:"file";O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:942;s:7:"user_id";i:1;s:8:"filename";s:44:"CI0ptlSUPho8Xw1NxTXAmZAmmZhNikdaJPk3oRZq.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/4XvY2TrJoQrlfDvxUjq0QiZQTigoVB1ZzGZ6vYZz.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:6:"186398";s:10:"created_at";s:19:"2025-07-02 16:42:12";s:10:"updated_at";s:19:"2025-07-02 16:42:12";}s:11:" * original";a:10:{s:2:"id";i:942;s:7:"user_id";i:1;s:8:"filename";s:44:"CI0ptlSUPho8Xw1NxTXAmZAmmZhNikdaJPk3oRZq.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/4XvY2TrJoQrlfDvxUjq0QiZQTigoVB1ZzGZ6vYZz.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:6:"186398";s:10:"created_at";s:19:"2025-07-02 16:42:12";s:10:"updated_at";s:19:"2025-07-02 16:42:12";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:7:"options";i:1;s:18:"call_to_action_url";i:2;s:18:"open_in_new_window";i:3;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:"translatedAttributes";a:5:{i:0;s:7:"file_id";i:1;s:9:"caption_1";i:2;s:9:"caption_2";i:3;s:9:"direction";i:4;s:19:"call_to_action_text";}s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"speed";i:1;s:8:"autoplay";i:2;s:14:"autoplay_speed";i:3;s:4:"fade";i:4;s:4:"dots";i:5;s:6:"arrows";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:"translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * defaultLocale";N;}i:1;a:1:{s:26:"_fleetcart_cache_sliders.4";s:26:"_fleetcart_cache_sliders.4";}i:2;i:1784320032;}
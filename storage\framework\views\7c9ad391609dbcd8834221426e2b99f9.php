<section x-data="Hero" class="home-section-wrap">
    <div class="container">
        <div class="row">
            <div class="home-section-inner">
                <div class="home-slider-wrap">
                    <div
                        class="home-slider overflow-hidden swiper"
                        data-speed="<?php echo e($slider->speed); ?>"
                        data-autoplay="<?php echo e($slider->autoplay ? 'true' : 'false'); ?>"
                        data-autoplay-speed="<?php echo e($slider->autoplay_speed); ?>"
                        data-dots="<?php echo e($slider->dots ? 'true' : 'false'); ?>"
                        data-arrows="<?php echo e($slider->arrows ? 'true' : 'false'); ?>"
                    >
                        <div class="swiper-wrapper">
                            <?php $__currentLoopData = $slider->slides; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $slide): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <a href="<?php echo e($slide->call_to_action_url); ?>" class="swiper-slide">
                                    <div
                                        class="slider-bg-image"
                                        data-swiper-parallax-x="50%"
                                        style="background-image: url(<?php echo e($slide->file->path); ?>)"
                                    >
                                    </div>

                                    <div class="slide-content <?php echo e($slide->isAlignedLeft() ? 'align-left' : 'align-right'); ?>">
                                        <div class="captions">
                                            <span
                                                class="caption caption-1"
                                                data-swiper-parallax-opacity="0.5"
                                            >
                                                <?php echo $slide->caption_1; ?>

                                            </span>

                                            <span
                                                class="caption caption-2"
                                                data-swiper-parallax-x="40%"
                                                data-swiper-parallax-opacity="0"
                                            >
                                                <?php echo e($slide->caption_2); ?>

                                            </span>
                                        </div>
                                    </div>
                                </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        
                        <?php if($slider->dots): ?> 
                            <div class="swiper-pagination"></div>
                        <?php endif; ?>

                        <?php if($slider->arrows): ?>
                            <div class="swiper-button-prev"></div>
                            <div class="swiper-button-next"></div>
                        <?php endif; ?>
                    </div>
                </div>

                <?php echo $__env->make('storefront::public.home.sections.slider_banners', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
        </div>
    </div>
</section>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/home/<USER>/hero.blade.php ENDPATH**/ ?>
<?php $__env->startSection('title'); ?>
    <?php if(request()->has('query')): ?>
        <?php echo e(trans('storefront::products.search_results_for')); ?>: "<?php echo e(request('query')); ?>"
    <?php else: ?>
        <?php echo e(trans('storefront::products.shop')); ?>

    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <section
        x-data="ProductIndex({
            initialQuery: '<?php echo e(addslashes(request('query'))); ?>',
            initialBrandName: '<?php echo e(addslashes($brandName ?? '')); ?>',
            initialBrandBanner: '<?php echo e($brandBanner ?? ''); ?>',
            initialBrandSlug: '<?php echo e(request('brand')); ?>',
            initialCategoryName: '<?php echo e(addslashes($categoryName ?? '')); ?>',
            initialCategoryBanner: '<?php echo e($categoryBanner ?? ''); ?>',
            initialCategorySlug: '<?php echo e(request('category')); ?>',
            initialTagName: '<?php echo e(addslashes($tagName ?? '')); ?>',
            initialTagSlug: '<?php echo e(request('tag')); ?>',
            initialAttribute: <?php echo e(json_encode(request('attribute', []))); ?>,
            minPrice: <?php echo e($minPrice); ?>,
            maxPrice: <?php echo e($maxPrice); ?>,
            initialSort: '<?php echo e(request('sort', 'latest')); ?>',
            initialPage: <?php echo e(request('page', 1)); ?>,
            initialPerPage: <?php echo e(request('perPage', 20)); ?>,
            initialViewMode: '<?php echo e(request('viewMode', 'grid')); ?>',
        })"
        class="product-search-wrap"
    >
        <div class="container">
            <div class="product-search">
                <div class="product-search-left">
                    <?php if($categories->isNotEmpty()): ?>
                        <div class="d-none d-lg-block browse-categories-wrap">
                            <h4 class="section-title">
                                <?php echo e(trans('storefront::products.browse_categories')); ?>

                            </h4>

                            <?php echo $__env->make('storefront::public.products.index.browse_categories', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php echo $__env->make('storefront::public.products.index.filter', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('storefront::public.products.index.latest_products', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>


                <div class="product-search-right">
                    <template x-if="brandBanner">
                        <div class="d-none d-lg-block categories-banner">
                            <img :src="brandBanner" alt="Brand banner">
                        </div>
                    </template>
                    
                    <template x-if="!brandBanner && categoryBanner">
                        <div class="d-none d-lg-block categories-banner">
                            <img :src="categoryBanner" alt="Category banner">
                        </div>
                    </template>

                    <?php echo $__env->make('storefront::public.products.index.search_result', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('globals'); ?>
    <script>
        FleetCart.langs['storefront::products.showing_results'] = '<?php echo e(trans("storefront::products.showing_results")); ?>';
    </script>
    
    <?php echo app('Illuminate\Foundation\Vite')([
        'modules/Storefront/Resources/assets/public/sass/pages/products/index/main.scss',
        'modules/Storefront/Resources/assets/public/js/pages/products/index/main.js',
    ]); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('storefront::public.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/products/index.blade.php ENDPATH**/ ?>
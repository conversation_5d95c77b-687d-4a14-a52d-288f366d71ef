<!DOCTYPE html>
<html lang="<?php echo e(locale()); ?>">
<head>
    <base href="<?php echo e(config('app.url')); ?>">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">

    <title>
        <?php if (! empty(trim($__env->yieldContent('title')))): ?>
            <?php echo $__env->yieldContent('title'); ?> - <?php echo e(setting('store_name')); ?>

        <?php else: ?>
            <?php echo e(setting('store_name')); ?>

        <?php endif; ?>
    </title>

    <?php echo $__env->yieldPushContent('meta'); ?>
    <?php $config = (new \Modules\Support\Services\ManifestService())->generate(); echo $__env->make( 'support::pwa.meta' , ['config' => $config])->render(); ?>

    <link rel="shortcut icon" href="<?php echo e($favicon); ?>" type="image/x-icon">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="<?php echo e(font_url(setting('storefront_display_font', 'Poppins'))); ?>" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">

    <?php echo $__env->make('storefront::public.partials.variables', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo app('Illuminate\Foundation\Vite')([
        'modules/Storefront/Resources/assets/public/sass/app.scss',
        'modules/Storefront/Resources/assets/public/js/app.js',
        'modules/Storefront/Resources/assets/public/js/main.js'
    ]); ?>

    <?php echo $__env->yieldPushContent('styles'); ?>

    <?php echo setting('custom_header_assets'); ?>


    <link rel="stylesheet" href="<?php echo e(asset('whatsapp/plugin/czm-chat-support.css')); ?>">

    <script>
        window.FleetCart = {
            baseUrl: '<?php echo e(config('app.url')); ?>',
            rtl: <?php echo e(is_rtl() ? 'true' : 'false'); ?>,
            storeName: '<?php echo e(setting('store_name')); ?>',
            storeLogo: '<?php echo e($logo); ?>',
            currency: '<?php echo e(currency()); ?>',
            locale: '<?php echo e(locale()); ?>',
            loggedIn: <?php echo e(auth()->check() ? 'true' : 'false'); ?>,
            csrfToken: '<?php echo e(csrf_token()); ?>',
            cart: <?php echo $cart; ?>,
            wishlist: <?php echo $wishlist; ?>,
            compareList: <?php echo $compareList; ?>,
            langs: {
                'storefront::storefront.something_went_wrong': '<?php echo e(trans('storefront::storefront.something_went_wrong')); ?>',
                'storefront::layouts.more_results': '<?php echo e(trans('storefront::layouts.more_results')); ?>'
            },
        };
    </script>

    <?php echo $schemaMarkup->toScript(); ?>


    <?php echo $__env->yieldPushContent('globals'); ?>

    <script type="module">
        Alpine.start();
    </script>

    <?php echo app('Tighten\Ziggy\BladeRouteGenerator')->generate(); ?>
</head>

<body
    dir="<?php echo e(is_rtl() ? 'rtl' : 'ltr'); ?>"
    class="page-template <?php echo e(is_rtl() ? 'rtl' : 'ltr'); ?>"
    data-theme-color="<?php echo e($themeColor->toHexString()); ?>"
>
<div x-data="App" class="wrapper">
    <?php echo $__env->make('storefront::public.layouts.top_nav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('storefront::public.layouts.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('storefront::public.layouts.navigation', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('storefront::public.layouts.breadcrumb', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo $__env->yieldContent('content'); ?>

    <?php echo $__env->make('storefront::public.home.sections.newsletter_subscription', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('storefront::public.layouts.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <div
        class="overlay"
        :class="{ active: $store.layout.overlay }"
        @click="hideOverlay"
    >
    </div>

    <?php echo $__env->make('storefront::public.layouts.sidebar_menu', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('storefront::public.layouts.localization', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php if(!request()->routeIs('checkout.create')): ?>
        <?php echo $__env->make('storefront::public.layouts.sidebar_cart', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <?php echo $__env->make('storefront::public.layouts.alert', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('storefront::public.layouts.newsletter_popup', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('storefront::public.layouts.cookie_bar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('storefront::public.layouts.scroll_to_top', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</div>

<!-- *** [START] REQUIRED DIV *** -->
<div id="example"></div>
<!-- *** [END] REQUIRED DIV *** -->

<?php echo $__env->yieldPushContent('pre-scripts'); ?>
<?php echo $__env->yieldPushContent('scripts'); ?>

<?php echo setting('custom_footer_assets'); ?>


<script src="<?php echo e(asset('whatsapp/plugin/components/jquery/jquery-1.9.0.min.js')); ?>"></script>
<script src="<?php echo e(asset('whatsapp/plugin/components/moment/moment.min.js')); ?>"></script>
<script src="<?php echo e(asset('whatsapp/plugin/components/moment/moment-timezone-with-data.min.js')); ?>"></script>
<script src="<?php echo e(asset('whatsapp/plugin/czm-chat-support.min.js')); ?>"></script>

<script>
    $('#example').czmChatSupport({

        /* Button Settings */
        button: {
            position: "<?php echo e(is_rtl() ? 'left' : 'right'); ?>", /* left, right or false. "position:false" does not pin to the left or right */
            style: 1, /* Button style. Number between 1 and 7 */
            src: '<i class="fab fa-whatsapp"></i>', /* Image, Icon or SVG */
            backgroundColor: "#10c379", /* Html color code */
            effect: 1, /* Button effect. Number between 1 and 7 */
            notificationNumber: false, /* Custom text or false. To remove, (notificationNumber:false) */
            speechBubble: false, /* To remove, (speechBubble:false) */
            pulseEffect: true, /* To remove, (pulseEffect:false) */
            text: { /* For Button style larger than 1 */
                title: "بحاجة لمساعدة ؟ تواصل معنا", /* Writing is required */
                description: "الدعم الفني", /* To remove, (description:false) */
                online: "متصل", /* To remove, (online:false) */
                offline: "غير متصل" /* To remove, (offline:false) */
            }
        },

        /* Popup Settings */
        popup: {
            automaticOpen: false, /* true or false (Open popup automatically when the page is loaded) */
            outsideClickClosePopup: true, /* true or false (Clicking anywhere on the page will close the popup) */
            effect: 1, /* Popup opening effect. Number between 1 and 15 */
            header: {
                backgroundColor: "#10c379", /* Html color code */
            },

            /* Representative Settings */
            persons: [

                /* Copy for more representatives [::Start Copy::] */
                {
                    avatar: {
                        src: '<img src="<?php echo e(asset('whatsapp/assets/img/person/1.jpg')); ?>" alt="">',
                        backgroundColor: "#ffffff",
                        onlineCircle: true
                    },
                    text: {
                        title: "بحاجة الى مساعدة؟ تواصل معنا",
                        description: "الدعم الفني",

                        /* Used on one account only */
                        message: "مرحبا 🙂<br>كيف يمكنني مساعدتك ؟",
                        textbox: "أكتب للبدأ",
                        button: false
                    },
                    link: {
                        desktop: "https://web.whatsapp.com/send?phone=+************&text=Hi",
                        mobile: "https://wa.me/+************/?text=Hi"
                    },
                    onlineDay: {
                        sunday: "00:00-23:59",
                        monday: "00:00-23:59",
                        tuesday: "00:00-23:59",
                        wednesday: "00:00-23:59",
                        thursday: "00:00-23:59",
                        friday: "00:00-23:59",
                        saturday: "00:00-23:59"
                    }
                },
            ]
        },

        /* Other Settings */
        sound: false,
        changeBrowserTitle: false,
        cookie: false,
    });
</script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/layout.blade.php ENDPATH**/ ?>
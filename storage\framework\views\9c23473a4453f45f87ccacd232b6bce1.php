<style>
    :root {
        --base-font-family: "<?php echo e(setting('storefront_display_font', 'Poppins')); ?>", sans-serif;
        --color-primary: <?php echo e(tinycolor($themeColor->toString())->toHexString()); ?>;
        --color-primary-hover: <?php echo e(tinycolor($themeColor->toString())->darken(8)->toString()); ?>;
        --color-primary-alpha-10: <?php echo e(tinycolor($themeColor->toString())->setAlpha(0.10)->toString()); ?>;
        --color-primary-alpha-12: <?php echo e(tinycolor($themeColor->toString())->setAlpha(0.12)->toString()); ?>;
        --color-primary-alpha-15: <?php echo e(tinycolor($themeColor->toString())->setAlpha(0.15)->toString()); ?>;
        --color-primary-alpha-30: <?php echo e(tinycolor($themeColor->toString())->setAlpha(0.3)->toString()); ?>;
        --color-primary-alpha-80: <?php echo e(tinycolor($themeColor->toString())->setAlpha(0.8)->toString()); ?>;
    }
</style>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/partials/variables.blade.php ENDPATH**/ ?>
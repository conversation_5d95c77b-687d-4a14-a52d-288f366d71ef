<section class="top-nav-wrap">
    <div class="container">
        <div class="top-nav">
            <div class="d-flex justify-content-between">
                <div class="top-nav-left d-none d-lg-block">
                    <span><?php echo e(setting('storefront_welcome_text')); ?></span>
                </div>

                <div class="top-nav-right">
                    <ul class="list-inline top-nav-right-list"> 
                        <li>
                            <a href="<?php echo e(route('contact.create')); ?>">
                                <i class="las la-envelope"></i>

                                <?php echo e(trans('storefront::layouts.contact')); ?>

                            </a>
                        </li>

                        <?php if(is_multilingual()): ?>
                            <div
                                x-data="{
                                    open: false,
                                    selected: '<?php echo e(locale_get_display_language(locale())); ?>'
                                }"
                                class="dropdown custom-dropdown"
                                :class="{ active: open }"
                                @click.away="open = false"
                            >
                                <div
                                    class="btn btn-secondary dropdown-toggle"
                                    :class="{ active: open }"
                                    @click="open = !open"
                                >
                                    <i class="las la-language"></i>

                                    <?php echo e(locale_get_display_language(locale())); ?>


                                    <i class="las la-angle-down"></i>
                                </div>

                                <ul
                                    x-cloak
                                    x-show="open"
                                    x-transition
                                    class="dropdown-menu"
                                    :class="{ active: open }"
                                >
                                    <div class="dropdown-menu-scroll">
                                        <?php $__currentLoopData = supported_locales(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $locale => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if(locale_get_display_language(locale()) !== $language['name']): ?>
                                                <li
                                                    class="dropdown-item"
                                                    @click="
                                                        open = false;
                                                        selected = '<?php echo e($locale); ?>';
                                                        location = '<?php echo e(localized_url($locale)); ?>'
                                                    "
                                                >
                                                    <?php echo e($language['name']); ?>

                                                </li>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <?php if(is_multi_currency()): ?>
                            <div
                                x-data="{
                                    open: false,
                                    selected: '<?php echo e(currency()); ?>'
                                }"
                                class="dropdown custom-dropdown"
                                :class="{ active: open }"
                                @click.away="open = false"
                            >
                                <div
                                    class="btn btn-secondary dropdown-toggle"
                                    :class="{ active: open }"
                                    @click="open = !open"
                                >
                                    <i class="las la-money-bill"></i>

                                    <?php echo e(currency()); ?>


                                    <i class="las la-angle-down"></i>
                                </div>

                                <ul
                                    x-cloak
                                    x-show="open"
                                    x-transition
                                    class="dropdown-menu"
                                    :class="{ active: open }"
                                >
                                    <div class="dropdown-menu-scroll">
                                        <?php $__currentLoopData = setting('supported_currencies'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if(currency() !== $currency): ?>
                                                <li
                                                    class="dropdown-item"
                                                    @click="
                                                        open = false;
                                                        selected = '<?php echo e($currency); ?>';
                                                        location = '<?php echo e(route('current_currency.store', ['code' => $currency])); ?>'
                                                    "
                                                >
                                                    <?php echo e($currency); ?>

                                                </li>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <?php if(auth()->guard()->check()): ?>
                            <li class="top-nav-account">
                                <a href="<?php echo e(route('account.dashboard.index')); ?>">
                                    <i class="las la-user"></i>

                                    <?php echo e(trans('storefront::layouts.account')); ?>

                                </a>
                            </li>
                        <?php else: ?>
                            <li>
                                <a href="<?php echo e(route('login')); ?>">
                                    <i class="las la-sign-in-alt"></i>

                                    <?php echo e(trans('storefront::layouts.login_register')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/layouts/top_nav.blade.php ENDPATH**/ ?>
<div
    x-data="VerticalProducts(<?php echo e($columnNumber); ?>)"
    x-show="hasAnyProduct"
    class="<?php echo e($flashSaleEnabled ? 'col-xl-4 col-lg-6' : 'col-xl-6 col-lg-6'); ?>"
>
    <template x-if="hasAnyProduct">
        <div class="vertical-products">
            <div class="vertical-products-header">
                <h4 class="section-title"><?php echo e($title); ?></h4>
            </div>

            <div class="vertical-products-slider swiper" x-ref="verticalProducts">
                <div class="swiper-wrapper"> 
                    <template
                        x-for="(productChunks, index) in chunk(products, 5)"
                        :key="index"
                    >
                        <div class="swiper-slide">
                            <template
                                x-for="product in productChunks"
                                :key="product.id"
                            >
                                <?php echo $__env->make('storefront::public.partials.vertical_products', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </template>
                        </div>
                    </template>
                </div>

                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
            </div>
        </div>
    </template>
</div><?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/home/<USER>/flash_sale/vertical_products.blade.php ENDPATH**/ ?>
<?php

namespace FleetCart\Observers;

use Modules\Product\Entities\Product;

class ProductObserver
{
    /**
     * Handle the Product "saved" event.
     *
     * @param  \Modules\Product\Entities\Product  $product
     * @return void
     */
    public function saved(Product $product)
    {
        $product->categories()->sync(request()->input('categories', []));
        $product->productBrands()->sync(request()->input('brands', []));
        $product->tags()->sync(request()->input('tags', []));
        $product->upSellProducts()->sync(request()->input('up_sells', []));
        $product->crossSellProducts()->sync(request()->input('cross_sells', []));
        $product->relatedProducts()->sync(request()->input('related_products', []));

        $product->withoutEvents(function () use ($product) {
            $product->update([
                'selling_price' => ($product->hasSpecialPrice() ? $product->getSpecialPrice() : $product->price)->amount(),
            ]);
        });
    }
}

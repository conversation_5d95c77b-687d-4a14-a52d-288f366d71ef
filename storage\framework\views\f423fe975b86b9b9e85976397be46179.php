<?php if(setting('cookie_bar_enabled') && json_decode(Cookie::get('show_cookie_bar', true))): ?>
    <div x-data="CookieBar" class="cookie-bar-wrap" :class="{ show: show }">
        <div class="container d-flex justify-content-center">
            <div class="col-xl-10 col-lg-12">
                <div class="row justify-content-center">
                    <div class="cookie-bar">
                        <div class="cookie-bar-text">
                            <?php echo trans('storefront::layouts.the_website_uses_cookies'); ?>

                        </div>

                        <div class="cookie-bar-action">
                            <button class="btn btn-default btn-decline" @click="decline">
                                <?php echo e(trans('storefront::layouts.decline')); ?>

                            </button>

                            <button class="btn btn-primary btn-accept" @click="accept">
                                <?php echo e(trans('storefront::layouts.accept')); ?>

                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/layouts/cookie_bar.blade.php ENDPATH**/ ?>
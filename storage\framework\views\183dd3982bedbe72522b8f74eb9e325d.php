<div
    x-data="{ open: false }"
    class="category-nav <?php echo e(request()->routeIs('home') ? 'show' : 'category-dropdown-menu'); ?>"
>
    <div class="category-nav-inner" @click="open = !open">
        <span><?php echo e(trans('storefront::layouts.all_categories_header')); ?></span>
        
        <i class="las la-bars"></i>
    </div>

    <?php if($categoryMenu->menus()->isNotEmpty()): ?>
        <div
            class="category-dropdown-wrap"
            :class="{ show: open }"
        >
            <div class="category-dropdown">
                <ul class="list-inline mega-menu vertical-megamenu">
                    <?php $__currentLoopData = $categoryMenu->menus(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php echo $__env->make('storefront::public.layouts.navigation.menu', ['type' => 'category_menu'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <li class="more-categories">
                        <a
                            href="<?php echo e(route('categories.index')); ?>"
                            class="menu-item"
                            title="<?php echo e(trans('storefront::layouts.all_categories')); ?>"
                        >
                            <span class="menu-item-icon">
                                <i class="las la-plus-square"></i>
                            </span>

                            <?php echo e(trans('storefront::layouts.all_categories')); ?>

                        </a>
                    </li>
                </ul>
            </div>
        </div>
    <?php endif; ?>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/layouts/navigation/category_menu.blade.php ENDPATH**/ ?>
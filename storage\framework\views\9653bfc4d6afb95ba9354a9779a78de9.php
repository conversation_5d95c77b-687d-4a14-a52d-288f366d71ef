<div x-data="ProductCard(product)" class="vertical-product-card">
    <a :href="productUrl" class="product-image">
        <img
            :src="baseImage"
            :class="{ 'image-placeholder': !hasBaseImage }"
            :alt="productName"
            loading="lazy"
        />

        <div class="product-image-layer"></div>
    </a>

    <div class="product-info">
        <a :href="productUrl" class="product-name">
            <h6 x-text="productName"></h6>
        </a>

        <?php echo $__env->make('storefront::public.partials.product_rating', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        
        <div class="product-price" x-html="productPrice"></div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/partials/vertical_products.blade.php ENDPATH**/ ?>
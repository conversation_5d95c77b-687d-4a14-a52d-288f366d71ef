<aside
    x-data="SidebarCart"
    class="sidebar-cart-wrap"
    :class="{ active: $store.layout.isOpenSidebarCart }"
>
    <div class="sidebar-cart-top">
        <h4 class="title">
            <?php echo e(trans('storefront::layouts.my_cart')); ?>


            <div class="count" x-text="$store.state.cartQuantity">
                <?php echo e($cart->toArray()['quantity']); ?>

            </div>
        </h4>

        <div class="sidebar-cart-close" @click="$store.layout.closeSidebarCart()">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M15.8338 4.16663L4.16705 15.8333M4.16705 4.16663L15.8338 15.8333" stroke="#0E1E3E" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg> 
        </div>
    </div>
        
    <div
        class="sidebar-cart-middle"
        :class="cartIsEmpty ? 'empty' : 'custom-scrollbar'"
    >
        <template x-if="!cartIsEmpty">
            <div class="sidebar-cart-items-wrap">
                <?php echo $__env->make('storefront::public.layouts.sidebar_cart.sidebar_cart_items', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
        </template>

        <template x-if="cartIsEmpty">
            <div class="empty-message">
                <?php echo $__env->make('storefront::public.layouts.sidebar_cart.empty_logo', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                <h4><?php echo e(trans('storefront::cart.your_cart_is_empty')); ?></h4>
            </div>
        </template>
    </div>

    <template x-if="!cartIsEmpty">
        <div class="sidebar-cart-bottom">
            <h5 class="sidebar-cart-subtotal">
                <?php echo e(trans('storefront::layouts.subtotal')); ?>


                <span x-text="formatCurrency($store.state.cartSubTotal)"></span>
            </h5>

            <div class="sidebar-cart-actions">
                <button type="button" @click="clearCart" class="btn btn-clear-cart">
                    <?php echo e(trans('storefront::layouts.clear_cart')); ?>

                </button>

                <template x-if="! route().current('cart.index')">
                    <a :href="route('cart.index')" class="btn btn-default btn-view-cart">
                        <?php echo e(trans('storefront::layouts.view_cart')); ?>

                    </a>
                </template>

                <a :href="route('checkout.create')" class="btn btn-primary btn-checkout">
                    <?php echo e(trans('storefront::layouts.checkout')); ?>

                </a>
            </div>
        </div>
    </template>
</aside>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/layouts/sidebar_cart.blade.php ENDPATH**/ ?>